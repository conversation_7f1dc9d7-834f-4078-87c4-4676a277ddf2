{"name": "stbe", "private": true, "version": "1.0.0", "description": "STBE GROUP", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start:zalo": "yarn && node setup.js --zalo --dev && zmp start -P 3005", "start:zalo-test": "yarn && node setup.js --zalo --test && zmp start -M test -P 3005", "start:zalo-d": "yarn && node setup.js --zalo && zmp start -D", "login:zalo": "zmp login", "deploy:zalo-test": "yarn && node setup.js --zalo --test && zmp deploy --mode=test", "deploy:zalo": "yarn && node setup.js --zalo --production && zmp deploy", "start:web": "yarn && node setup.js --web --dev && vite --port 3005", "build:web": "yarn && node setup.js --web --production && vite build --mode production", "build-test:web": "yarn && node setup.js --web --test && vite build --mode test", "preview:web": "vite preview", "start-server": "cd .. && cd gmgc-strapi && yarn && yarn watch-admin", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "eslint": "eslint . --fix"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "3.1.1", "@mui/icons-material": "^5.15.12", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.12", "@mui/x-tree-view": "^7.12.1", "@reduxjs/toolkit": "^2.2.3", "@vitejs/plugin-react": "^1.3.0", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "lodash": "^4.17.21", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18.2.0", "react-hook-form": "7.49.3", "react-hot-toast": "^2.4.1", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-slick": "^0.30.2", "recoil": "^0.7.7", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "use-debounce": "^10.0.4", "vite": "^5.2.11", "yup": "^1.3.3", "zmp-qrcode": "^3.0.0", "zmp-sdk": "^2.36.0", "zmp-ui": "^1.9.2", "zmp-vite-plugin": "^1.1.4", "react-image-gallery": "^1.3.0"}, "devDependencies": {"@eslint/compat": "^1.0.1", "@eslint/js": "^9.3.0", "@types/lodash": "^4.17.4", "@types/node": "^20.12.7", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react-refresh": "^1.3.6", "cross-env": "^7.0.3", "eslint": "9.x", "eslint-plugin-react": "^7.34.1", "globals": "^15.3.0", "postcss-preset-env": "^6.7.0", "prettier": "^2.7.1", "typescript": "5.4.5", "typescript-eslint": "^7.10.0", "zmp-cli": "^3.15.5"}, "prettier": {"singleQuote": false, "tabWidth": 2, "semi": true}}